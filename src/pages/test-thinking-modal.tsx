import React from 'react'
import { IonContent, IonHeader, IonPage, IonTitle, IonToolbar } from '@ionic/react'
import ChatBubble, { MessageData } from '@/pages/onboarding/ChatBubble'

const TestThinkingModalPage: React.FC = () => {
  // 测试数据 - 包含 thinking 内容的消息
  const testMessage: MessageData = {
    text: '这是一个测试消息，用来验证 thinking 模态窗口功能。',
    think: {
      segments: [
        '首先，我需要理解用户的需求...',
        '然后，我要分析可能的解决方案...',
        '接下来，我会考虑最佳的实现方式...',
        '最后，我将提供一个完整的回答。'
      ],
      startTime: Date.now() - 5000 // 5秒前开始思考
    }
  }

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>测试 Thinking 模态窗口</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">Thinking 模态窗口测试</h2>
          
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              下面的消息包含 thinking 内容。等待 thinking 完成并收起后，点击 "思考完毕" 标题可以打开模态窗口查看完整的思考过程。
            </p>
            
            {/* 测试 ChatBubble 组件 */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <ChatBubble 
                message={testMessage}
                onThinkingComplete={(duration) => {
                  console.log('思考完成，用时:', duration, '秒')
                }}
                onTextComplete={() => {
                  console.log('文本输出完成')
                }}
                showText={true}
              />
            </div>
          </div>

          <div className="mt-8 space-y-2">
            <h3 className="text-base font-medium">测试说明:</h3>
            <ul className="list-disc space-y-1 pl-6 text-sm text-gray-600">
              <li>thinking 内容会逐段显示，每段之间有1秒间隔</li>
              <li>thinking 完成后会自动收起并显示用时</li>
              <li>点击收起后的标题可以打开模态窗口</li>
              <li>模态窗口支持下滑手势关闭</li>
              <li>模态窗口显示所有思考片段的完整内容</li>
            </ul>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default TestThinkingModalPage
