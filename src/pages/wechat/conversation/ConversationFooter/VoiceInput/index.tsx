import React, { useCallback, useRef, useState } from 'react'
import { useVoiceRecording } from '@/hooks/useVoiceRecording'
import { showToast } from '@/wechatComponents/Toast'
import { ensureMicrophonePermission, checkMicrophonePermission } from '@/utils/androidPermissions'

// 在文件顶部插入样式
const waveformStyle = `
.waveform-container {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 50px;
}
.waveform-bubble {
    background-color: #67e35b;
    padding: 20px 35px;
    border-radius: 15px;
    position: relative;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}
.waveform-bubble::after {
    content: '';
    position: absolute;
    bottom: -18px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 10px;
    border-style: solid;
    border-color: #67e35b transparent transparent transparent;
}
.waveform {
    display: flex;
    align-items: center;
    height: 30px;
}
.bar {
    width: 3px;
    height: 5px;
    background-color: rgba(0, 0, 0, 0.6);
    margin: 0 2px;
    border-radius: 2px;
    animation: pulse 1.2s ease-in-out infinite;
}
.bar:nth-child(1) { animation-delay: 0.1s; }
.bar:nth-child(2) { animation-delay: 0.3s; }
.bar:nth-child(3) { animation-delay: 0.5s; }
.bar:nth-child(4) { animation-delay: 0.2s; }
.bar:nth-child(5) { animation-delay: 0.4s; }
.bar:nth-child(6) { animation-delay: 0.1s; }
.bar:nth-child(7) { animation-delay: 0.3s; height: 15px; }
.bar:nth-child(8) { animation-delay: 0s; height: 20px; }
.bar:nth-child(9) { animation-delay: 0.2s; height: 25px; }
.bar:nth-child(10) { animation-delay: 0.4s; height: 20px; }
.bar:nth-child(11) { animation-delay: 0.1s; height: 15px; }
.bar:nth-child(12) { animation-delay: 0.3s; }
.bar:nth-child(13) { animation-delay: 0.5s; }
.bar:nth-child(14) { animation-delay: 0.2s; }
.bar:nth-child(15) { animation-delay: 0.4s; }
.bar-silent {
    animation: pulse-silent 1.2s ease-in-out infinite;
}
@keyframes pulse {
    0%, 100% { transform: scaleY(0.5); }
    50% { transform: scaleY(1.5); }
}
@keyframes pulse-silent {
    0%, 100% { transform: scaleY(0.3); }
    50% { transform: scaleY(0.6); }
}
.controls-arc-bg {
    position: absolute;
    width: 260%;
    height: 300px;
    left: 50%;
    bottom: -150px;
    transform: translateX(-50%);
    background-color: rgba(75, 75, 75, 0.95);
    border-radius: 50%;
}
.send-arc-active {
    background-color: rgba(7, 193, 96, 0.95) !important;
}
.circular-button {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}
.circular-button-cancel {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}
.circular-button-cancel.active {
    background-color: #ef4444;
    color: white;
    transform: scale(1.1);
}
.circular-button-text {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}
.circular-button-text.active {
    background-color: #3b82f6;
    color: white;
    transform: scale(1.1);
}
`;

interface VoiceInputProps {
  onResult: (text: string) => void
  onError?: (error: string) => void
}

// 全屏语音面板的操作区域
const PANEL_STATUS = {
  SEND: 'SEND',
  CANCEL: 'CANCEL',
  TEXT: 'TEXT',
} as const
type PanelStatus = typeof PANEL_STATUS[keyof typeof PANEL_STATUS] | ''

const VoiceInput: React.FC<VoiceInputProps> = ({ onResult, onError }) => {
  const {
    isRecording,
    hasPermission,
    error,
    startRecording,
    stopRecording,
    cancelRecording,
    setOnResult,
    isSupported,
    audioLevels,
  } = useVoiceRecording()

  // 权限状态
  const [permissionChecked, setPermissionChecked] = useState(false)
  // 是否显示全屏面板
  const [showPanel, setShowPanel] = useState(false)
  // 当前滑动到的区域
  const [panelStatus, setPanelStatus] = useState<PanelStatus>('')
  // 用 ref 记录松手时的最终操作意图，避免 showPanel 状态提前变更导致丢失
  const panelStatusRef = useRef<PanelStatus>('')
  // 语音转文字内容
  const [voiceText, setVoiceText] = useState('')
  // 是否显示转文字编辑框
  const [showTextEdit, setShowTextEdit] = useState(false)
  // 按钮按下状态
  const isHoldingRef = useRef(false)
  // 记录触摸/鼠标起始点
  const startPointRef = useRef<{ x: number; y: number } | null>(null)

  // 检测是否为静音状态
  const isSilent = React.useMemo(() => {
    if (!audioLevels || audioLevels.length === 0) return false
    const minLevel = Math.min(...audioLevels)
    const maxLevel = Math.max(...audioLevels)
    const levelVariance = maxLevel - minLevel
    const averageLevel = audioLevels.reduce((sum, level) => sum + level, 0) / audioLevels.length
    return levelVariance < 5 || averageLevel < 25
  }, [audioLevels])

  // 设置语音识别回调
  React.useEffect(() => {
    setOnResult((text) => {
      console.log('[VoiceInput] setOnResult 回调触发，text:', text, 'panelStatusRef:', panelStatusRef.current)
      setVoiceText(text)
      // 只依赖 panelStatusRef 判断
      if (panelStatusRef.current === PANEL_STATUS.SEND) {
        console.log('[VoiceInput] 发送状态，调用 onResult:', text)
        onResult(text)
        setVoiceText('')
        setPanelStatus('')
        panelStatusRef.current = ''
      }
      if (panelStatusRef.current === PANEL_STATUS.TEXT) {
        console.log('[VoiceInput] 转文字状态，弹出编辑框，text:', text)
        setShowTextEdit(true)
        setPanelStatus('')
        panelStatusRef.current = ''
      }
    })
  }, [setOnResult, onResult])

  // 权限检查（只检查，不弹窗）
  const checkPermissionOnly = useCallback(async (): Promise<boolean> => {
    try {
      const result = await checkMicrophonePermission()
      return result.hasPermission || false
    } catch {
      return false
    }
  }, [])

  // 权限检查和申请（只在点击按钮时调用）
  const checkAndRequestPermission = useCallback(async (): Promise<boolean> => {
    try {
      const has = await checkPermissionOnly()
      if (has) return true
      const granted = await ensureMicrophonePermission(true)
      if (!granted) {
        showToast({ type: 'error', content: '麦克风权限被拒绝，无法录音。请在设置中开启麦克风权限。' })
        onError?.('麦克风权限被拒绝，无法录音')
        return false
      }
      return true
    } catch {
      showToast({ type: 'error', content: '权限检查失败，请重试' })
      onError?.('权限检查失败')
      return false
    }
  }, [onError, checkPermissionOnly])

  // 鼠标/触摸按下
  const handlePressStart = async (e: React.MouseEvent | React.TouchEvent) => {
    e.stopPropagation()
    if (!isSupported()) {
      onError?.('浏览器不支持语音录制')
      return
    }
    // 检查权限
    const has = await checkAndRequestPermission()
    setPermissionChecked(true)
    if (!has) return
    // 有权限，显示面板并开始录音
    setShowPanel(true)
    setPanelStatus(PANEL_STATUS.SEND)
    isHoldingRef.current = true
    // 记录起始点
    const point = 'touches' in e ? e.touches[0] : e
    startPointRef.current = { x: point.clientX, y: point.clientY }
    await startRecording()
    // 仅PC端：全局监听 mouseup，确保松开时能正确关闭面板
    if (!('ontouchstart' in window)) {
      window.addEventListener('mouseup', handleGlobalMouseUp)
    }
  }

  // PC端全局 mouseup 处理
  const handleGlobalMouseUp = (e: MouseEvent) => {
    if (!isHoldingRef.current) return
    handlePressEnd()
    window.removeEventListener('mouseup', handleGlobalMouseUp)
  }

  // 松开按钮/手指
  const handlePressEnd = async (e?: React.MouseEvent | React.TouchEvent) => {
    if (!isHoldingRef.current) return
    isHoldingRef.current = false
    setShowPanel(false)
    if (!('ontouchstart' in window)) {
      window.removeEventListener('mouseup', handleGlobalMouseUp)
    }
    if (panelStatus === PANEL_STATUS.CANCEL) {
      console.log('[VoiceInput] 取消状态，调用 cancelRecording')
      await cancelRecording()
      showToast({ type: 'text', content: '已取消发送' })
      setPanelStatus('')
      panelStatusRef.current = ''
      return
    }
    // 记录松手时的最终意图
    panelStatusRef.current = panelStatus
    // 发送和转文字都先停止录音，等待识别回调处理
    console.log('[VoiceInput] handlePressEnd 停止录音，panelStatus:', panelStatus)
    await stopRecording()
    // 其余逻辑在 setOnResult 回调中处理
  }

  // 滑动手势处理
  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isHoldingRef.current || !showPanel) return
    const point = 'touches' in e ? e.touches[0] : e
    const { clientX, clientY } = point
    // 获取面板区域
    const panel = document.getElementById('voice-panel')
    if (!panel) return
    const rect = panel.getBoundingClientRect()
    // 区域划分：左1/3为取消，右1/3为转文字，中间为发送
    const x = clientX - rect.left
    const y = clientY - rect.top
    const width = rect.width
    let status: PanelStatus = PANEL_STATUS.SEND
    if (x < width / 3) status = PANEL_STATUS.CANCEL
    else if (x > (width * 2) / 3) status = PANEL_STATUS.TEXT
    else status = PANEL_STATUS.SEND
    setPanelStatus(status)
  }

  // 编辑转文字内容后手动发送
  const handleTextSend = () => {
    console.log('[VoiceInput] handleTextSend, voiceText:', voiceText)
    if (voiceText) onResult(voiceText)
    setShowTextEdit(false)
    setVoiceText('')
  }

  // 关闭转文字编辑
  const handleTextCancel = () => {
    setShowTextEdit(false)
    setVoiceText('')
  }

  // 主按钮渲染
  const renderMainButton = () => (
    <button
      className="flex-1 rounded bg-white px-4 py-1.5 text-center text-sm transition-colors select-none border border-gray-200 "
      onMouseDown={handlePressStart}
      // onMouseUp={handlePressEnd}  // 移除，改为全局 mouseup
      // onMouseLeave={handlePressEnd} // 移除，防止提前关闭
      onTouchStart={handlePressStart}
      onTouchEnd={handlePressEnd}
      onTouchCancel={handlePressEnd}
      onTouchMove={handleMove}
      onMouseMove={handleMove}
      type="button"
    >
      按住 说话
    </button>
  )

  // 全屏语音面板
  const renderVoicePanel = () => (
    <div
      id="voice-panel"
      className="fixed inset-0 z-50 flex flex-col items-center justify-end bg-black/60"
      style={{ touchAction: 'none' }}
      onMouseUp={handlePressEnd}
      onTouchEnd={handlePressEnd}
      onMouseMove={handleMove}
      onTouchMove={handleMove}
    >
      {/* 全局样式插入 */}
      <style>{waveformStyle}</style>
      
      {/* 绿色语音气泡波形动画 */}
      <div className="waveform-container mb-16 flex flex-col items-center">
        <div className="waveform-bubble flex flex-col items-center">
          <div className="waveform">
            {Array.from({ length: 15 }).map((_, i) => (
              <div key={i} className={`bar ${isSilent ? 'bar-silent' : ''}`} />
            ))}
          </div>
        </div>
        <div className="mt-4 text-2xl text-gray-300 font-bold">
          {panelStatus === PANEL_STATUS.CANCEL ? '松开取消' : 
           panelStatus === PANEL_STATUS.TEXT ? '松开转文字' : 
           '松开发送'}
        </div>
      </div>

      {/* 弧形控制区域容器 */}
      <div className="relative w-full flex justify-center items-end" style={{ height: '25vh', minHeight: '150px' }}>
        {/* 弧形背景 */}
        <div className={`controls-arc-bg transition-colors duration-200 ${panelStatus === PANEL_STATUS.SEND ? 'send-arc-active' : ''}`}></div>
        
        {/* 圆形按钮：取消和转文字 */}
        <div className="absolute top-1 left-0 right-0 flex justify-between px-4 z-10">
          {/* 取消按钮 */}
          <div className={`circular-button circular-button-cancel ${panelStatus === PANEL_STATUS.CANCEL ? 'active' : ''}`}>
            取消
          </div>
          
          {/* 转文字按钮 */}
          <div className={`circular-button circular-button-text ${panelStatus === PANEL_STATUS.TEXT ? 'active' : ''}`}>
            转文字
          </div>
        </div>
        
        {/* 发送文字标签 */}
        <div className={`absolute bottom-16 text-2xl font-bold z-10 transition-colors duration-200 ${panelStatus === PANEL_STATUS.SEND ? 'text-white' : 'text-black'}`}>
          发送
        </div>
      </div>
    </div>
  )

  // 语音转文字编辑框
  const renderTextEdit = () => (
    <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black/60">
      <div className="bg-white rounded-2xl p-8 w-80 max-w-full flex flex-col items-center shadow-xl">
        <div className="mb-4 text-lg font-bold">语音转文字</div>
        <textarea
          className="w-full h-24 border rounded p-2 mb-4 text-base"
          value={voiceText}
          onChange={e => setVoiceText(e.target.value)}
        />
        <div className="flex w-full justify-between">
          <button className="px-4 py-2 rounded bg-gray-200 mr-2" onClick={handleTextCancel}>取消</button>
          <button className="px-4 py-2 rounded bg-[#07C160] text-white" onClick={handleTextSend}>发送</button>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {renderMainButton()}
      {showPanel && renderVoicePanel()}
      {showTextEdit && renderTextEdit()}
    </>
  )
}

export default VoiceInput 