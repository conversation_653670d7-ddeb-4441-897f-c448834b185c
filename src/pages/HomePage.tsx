import { useState, useEffect } from 'react'
import useAuth from '@/tina/stores/authStore'
import { IonPage, IonContent } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import loginText from '@/assets/login_text.png'
import logo from '@/assets/logo.png'
import LoadingPage from '@/components/LoadingPage'
import LoginForm from '@/components/LoginForm'
import RegisterForm from '@/components/RegisterForm'

const HomePage = () => {
  const [isLoading, setIsLoading] = useState(true)
  const [showRegister, setShowRegister] = useState(false)
  const auth = useAuth()
  const history = useHistory()

  useEffect(() => {
    // 检查登录状态
    const checkAuthStatus = async () => {
      try {
        if (auth.isLoggedIn()) {
          // 如果已登录，跳转到引导页面
          history.push('/conversation/1')
          return
        }
      } catch (error) {
        console.error('检查登录状态失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthStatus()
  }, [auth, history])

  const handleLoginSuccess = () => {
    // 登录成功后跳转到引导页面
    history.push('/conversation/1')
  }

  const handleRegisterSuccess = () => {
    // 注册成功后跳转到引导页面
    history.push('/onboarding')
  }

  if (isLoading) {
    return (
      <IonPage>
        <IonContent>
          <LoadingPage />
        </IonContent>
      </IonPage>
    )
  }

  return (
    <IonPage>
      <IonContent>
        <div
          className='relative mx-auto h-full w-full sm:max-w-md'
          style={{ backgroundColor: 'rgba(208, 216, 220, 1)' }}
        >
          <div className='absolute left-1/2 top-10 mb-6 -translate-x-1/2'>
            <img
              src={logo}
              alt='Tina Logo'
              className='h-45 w-45 object-contain translate-x-10'

            />
          </div>
          {/* 下半部分的大圆弧背景 */}
          <div
            className='absolute bottom-0 left-0 right-0'
            style={{
              height: '80vh',
              backgroundColor: '#F6F4EE',
              borderTopRightRadius: '50% 100px',
            }}
          >
            {/* 登录/注册表单内容 */}
            <div className='relative z-10'>
              {/* 登录文字图片 */}
              <div className='m-8'>
                <img
                  src={loginText}
                  alt='Login Text'
                  className='h-8 object-contain'
                />
              </div>
        {showRegister ? (
          <RegisterForm
            onSuccess={handleRegisterSuccess}
            onSwitchToLogin={() => setShowRegister(false)}
          />
        ) : (
          <LoginForm
            onSuccess={handleLoginSuccess}
            onSwitchToRegister={() => setShowRegister(true)}
          />
        )}
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

export default HomePage
